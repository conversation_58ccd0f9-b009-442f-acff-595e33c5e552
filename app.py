# app.py - Main Application Entry Point
"""
WOSS Seismic Analysis Tool - GPU-Optimized Modular Application

This is the main entry point for the modular, GPU-accelerated WOSS Seismic Analysis Tool.
The application provides a complete workflow for seismic data analysis with GPU optimization.
"""

import streamlit as st
import logging
import sys
import os
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import common modules
from common.session_state import initialize_session_state
from common.constants import APP_TITLE, ANALYSIS_MODES
from utils.gpu_utils import initialize_gpu_system, GPU_AVAILABLE

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('woss_analysis.log')
    ]
)

# Page configuration
st.set_page_config(
    page_title=APP_TITLE,
    page_icon="🌊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state and GPU system
initialize_session_state()

# Initialize GPU system if not already done
if not st.session_state.get('gpu_initialized'):
    with st.spinner("Initializing GPU system..."):
        try:
            gpu_available, backend, device_info = initialize_gpu_system()
            st.session_state.gpu_available = gpu_available
            st.session_state.gpu_backend = backend
            st.session_state.gpu_device_info = device_info
            st.session_state.gpu_initialized = True
            logging.info(f"GPU system initialized: {backend} - Available: {gpu_available}")
        except Exception as e:
            st.session_state.gpu_available = False
            st.session_state.gpu_backend = "CPU Fallback"
            st.session_state.gpu_device_info = None
            st.session_state.gpu_initialized = True
            logging.error(f"GPU initialization failed: {e}")

# Main application header
st.title("🌊 WOSS Seismic Analysis Tool")
st.markdown("### GPU-Accelerated Spectral Analysis for Seismic Data")

# GPU status display
st.markdown("---")
col1, col2, col3 = st.columns(3)

with col1:
    if st.session_state.get('gpu_available'):
        st.success("🚀 GPU Acceleration Available")
    else:
        st.warning("💻 CPU Processing Mode")

with col2:
    backend = st.session_state.get('gpu_backend', 'Unknown')
    st.info(f"**Backend:** {backend}")

with col3:
    if st.session_state.get('gpu_device_info'):
        device_info = st.session_state.gpu_device_info
        if isinstance(device_info, dict) and 'name' in device_info:
            st.info(f"**Device:** {device_info['name']}")
        else:
            st.info(f"**Device:** {device_info}")
    else:
        st.info("**Device:** CPU")

st.markdown("---")

# Workflow overview
st.markdown("### 📋 Analysis Workflow")

# Create workflow status indicators
workflow_steps = [
    {
        "step": 1,
        "title": "Load Data",
        "description": "Upload SEG-Y file and configure headers",
        "page": "pages/1_load_data.py",
        "status_key": "header_loader",
        "icon": "📁"
    },
    {
        "step": 2,
        "title": "Configure Display",
        "description": "Set analysis parameters and visualization settings",
        "page": "pages/2_configure_display.py",
        "status_key": "plot_settings",
        "icon": "⚙️"
    },
    {
        "step": 3,
        "title": "Select Area",
        "description": "Choose analysis mode and define area of interest",
        "page": "pages/3_select_area.py",
        "status_key": "area_selected",
        "icon": "🎯"
    },
    {
        "step": 4,
        "title": "Analyze Data",
        "description": "Execute GPU-accelerated spectral analysis",
        "page": "pages/4_analyze_data.py",
        "status_key": "analysis_complete",
        "icon": "🚀"
    },
    {
        "step": 5,
        "title": "Export Results",
        "description": "Export analysis results in various formats",
        "page": "pages/5_export_results.py",
        "status_key": "export_complete",
        "icon": "📤"
    }
]

# Display workflow steps
for i, step_info in enumerate(workflow_steps):
    col1, col2, col3, col4 = st.columns([1, 3, 6, 2])
    
    with col1:
        # Status indicator
        is_complete = bool(st.session_state.get(step_info["status_key"]))
        if is_complete:
            st.success(f"{step_info['icon']} ✅")
        else:
            st.info(f"{step_info['icon']} ⏳")
    
    with col2:
        st.write(f"**Step {step_info['step']}**")
        st.write(step_info["title"])
    
    with col3:
        st.write(step_info["description"])
        
        # Show additional status information
        if step_info["step"] == 1 and st.session_state.get('segy_filename'):
            st.caption(f"Loaded: {st.session_state.segy_filename}")
        elif step_info["step"] == 3 and st.session_state.get('analysis_mode'):
            st.caption(f"Mode: {st.session_state.analysis_mode}")
        elif step_info["step"] == 4 and st.session_state.get('processing_time'):
            st.caption(f"Processed in: {st.session_state.processing_time:.1f}s")
        elif step_info["step"] == 5 and st.session_state.get('export_format'):
            st.caption(f"Format: {st.session_state.export_format}")
    
    with col4:
        # Navigation button
        if st.button(f"Go to Step {step_info['step']}", key=f"nav_step_{step_info['step']}", use_container_width=True):
            st.switch_page(step_info["page"])

st.markdown("---")

# Quick actions sidebar
with st.sidebar:
    st.header("🚀 Quick Actions")
    
    # Reset workflow
    if st.button("🔄 Reset Workflow", use_container_width=True, help="Clear all session data and start over"):
        # Clear all session state except GPU info
        gpu_info = {
            'gpu_available': st.session_state.get('gpu_available', False),
            'gpu_backend': st.session_state.get('gpu_backend', 'Unknown'),
            'gpu_device_info': st.session_state.get('gpu_device_info'),
            'gpu_initialized': st.session_state.get('gpu_initialized', False)
        }
        
        # Clear all session state
        for key in list(st.session_state.keys()):
            if key not in gpu_info:
                del st.session_state[key]
        
        # Restore GPU info and reinitialize
        st.session_state.update(gpu_info)
        initialize_session_state()
        
        st.success("✅ Workflow reset successfully!")
        st.rerun()
    
    st.markdown("---")
    
    # Current status summary
    st.header("📊 Current Status")
    
    # Count completed steps
    completed_steps = sum(1 for step in workflow_steps if st.session_state.get(step["status_key"]))
    total_steps = len(workflow_steps)
    progress_percentage = (completed_steps / total_steps) * 100
    
    st.metric("Progress", f"{completed_steps}/{total_steps} steps")
    st.progress(progress_percentage / 100)
    
    # Show current analysis mode if selected
    if st.session_state.get('analysis_mode'):
        st.write(f"**Analysis Mode:** {st.session_state.analysis_mode}")
    
    # Show GPU status
    if st.session_state.get('gpu_available'):
        st.success("GPU Ready")
    else:
        st.warning("CPU Mode")
    
    st.markdown("---")
    
    # Help and information
    st.header("ℹ️ Information")
    
    with st.expander("📖 About WOSS Analysis"):
        st.write("""
        **WOSS (Weighted-Optimum Spectral Shape)** is an advanced spectral analysis technique 
        for seismic data interpretation. This tool provides:
        
        - **GPU Acceleration**: Faster processing using CUDA/CuPy
        - **Multiple Analysis Modes**: Inline, crossline, AOI, polyline, and well markers
        - **Comprehensive Outputs**: 14+ spectral attributes
        - **Flexible Export**: SEG-Y, CSV, and Excel formats
        """)
    
    with st.expander("🔧 System Requirements"):
        st.write("""
        **Recommended:**
        - NVIDIA GPU with CUDA support
        - 8+ GB GPU memory for large datasets
        - Python 3.8+ with CuPy installed
        
        **Minimum:**
        - CPU processing (fallback mode)
        - 4+ GB system RAM
        - Python 3.8+ with NumPy/SciPy
        """)
    
    with st.expander("🚀 Performance Tips"):
        st.write("""
        **For Best Performance:**
        - Use GPU acceleration when available
        - Process smaller AOIs for faster results
        - Adjust batch sizes based on GPU memory
        - Use compressed exports for large datasets
        """)

# Footer
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666; padding: 20px;'>
        <p><strong>WOSS Seismic Analysis Tool</strong> - GPU-Accelerated Spectral Analysis</p>
        <p>Developed with Streamlit • Powered by CuPy/CUDA • Optimized for Performance</p>
    </div>
    """,
    unsafe_allow_html=True
)

# Debug information (only in development)
if st.checkbox("🔍 Show Debug Information", help="Display session state for debugging"):
    st.markdown("### 🐛 Debug Information")
    
    with st.expander("Session State"):
        # Filter out large objects for display
        debug_state = {}
        for key, value in st.session_state.items():
            if key in ['header_loader', 'analysis_results', 'trace_data']:
                debug_state[key] = f"<{type(value).__name__}> (size: {len(value) if hasattr(value, '__len__') else 'unknown'})"
            else:
                debug_state[key] = value
        
        st.json(debug_state)
    
    with st.expander("System Information"):
        import platform
        import psutil
        
        st.write(f"**Platform:** {platform.platform()}")
        st.write(f"**Python:** {platform.python_version()}")
        st.write(f"**CPU Count:** {psutil.cpu_count()}")
        st.write(f"**Memory:** {psutil.virtual_memory().total / (1024**3):.1f} GB")
        
        if st.session_state.get('gpu_available'):
            try:
                import cupy as cp
                device = cp.cuda.Device()
                st.write(f"**GPU Memory:** {device.mem_info[1] / (1024**3):.1f} GB")
            except:
                st.write("**GPU Memory:** Unknown")
