# utils/__init__.py
"""
GPU-optimized utilities package for WOSS Seismic Analysis Tool.

This package contains GPU-first processing utilities with CPU fallback capability.
All modules have been organized for modular architecture and GPU optimization.
"""

# GPU utilities - core GPU processing infrastructure
from .gpu_utils import (
    initialize_gpu_system,
    get_processing_backend,
    optimize_batch_size_for_mode,
    get_optimal_processing_config,
    clear_gpu_memory,
    get_processing_functions,
    GPU_AVAILABLE,
    GPU_DEVICE_INFO,
    CPU_FALLBACK_ENABLED
)

# GPU processing functions for all analysis modes
from .processing_gpu import (
    process_inline_analysis_gpu,
    process_crossline_analysis_gpu,
    process_aoi_analysis_gpu,
    process_polyline_analysis_gpu,
    process_traces_gpu_batch
)

# Import other utility modules for easy access
# Note: These will be enhanced in subsequent phases
try:
    from . import data_utils
    from . import processing
    from . import visualization
    from . import general_utils
    from . import export_utils
    from . import dlogst_spec_descriptor_gpu
    from . import dlogst_spec_descriptor_cpu
except ImportError as e:
    import logging
    logging.warning(f"Some utility modules not available: {e}")

__all__ = [
    'initialize_gpu_system',
    'get_processing_backend',
    'optimize_batch_size_for_mode',
    'get_optimal_processing_config',
    'clear_gpu_memory',
    'get_processing_functions',
    'GPU_AVAILABLE',
    'GPU_DEVICE_INFO',
    'CPU_FALLBACK_ENABLED',
    'process_inline_analysis_gpu',
    'process_crossline_analysis_gpu',
    'process_aoi_analysis_gpu',
    'process_polyline_analysis_gpu',
    'process_traces_gpu_batch',
    'data_utils',
    'processing',
    'visualization',
    'general_utils',
    'export_utils',
    'dlogst_spec_descriptor_gpu',
    'dlogst_spec_descriptor_cpu'
]
