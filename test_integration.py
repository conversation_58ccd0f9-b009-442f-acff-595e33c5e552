#!/usr/bin/env python3
"""
Integration Test for WOSS Seismic Analysis Tool

This script tests the integration between all page modules and verifies
that the workflow functions correctly from data loading to export.
"""

import sys
import os
import logging
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_imports():
    """Test that all required modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        # Test common modules
        from common.constants import APP_TITLE, ANALYSIS_MODES, AVAILABLE_OUTPUTS_ALL_MODES
        from common.session_state import initialize_session_state
        print("✅ Common modules imported successfully")
        
        # Test utility modules
        from utils.gpu_utils import initialize_gpu_system, get_optimal_processing_config
        from utils.data_utils import SegyHeaderLoader
        from utils.processing_gpu import process_inline_analysis_gpu
        from utils.export_utils import validate_export_data, create_csv_export
        from utils.visualization import create_analysis_plots
        print("✅ Utility modules imported successfully")
        
        # Test page modules (import as modules, not run them)
        import importlib.util
        
        page_files = [
            "pages/1_load_data.py",
            "pages/2_configure_display.py", 
            "pages/3_select_area.py",
            "pages/4_analyze_data.py",
            "pages/5_export_results.py"
        ]
        
        for page_file in page_files:
            if os.path.exists(page_file):
                spec = importlib.util.spec_from_file_location("test_page", page_file)
                module = importlib.util.module_from_spec(spec)
                # Don't execute the module, just check if it can be loaded
                print(f"✅ {page_file} can be loaded")
            else:
                print(f"❌ {page_file} not found")
                return False
        
        print("✅ All page modules can be loaded")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_gpu_initialization():
    """Test GPU system initialization."""
    print("\nTesting GPU initialization...")
    
    try:
        from utils.gpu_utils import initialize_gpu_system
        
        gpu_available, device_info = initialize_gpu_system()
        backend = "CuPy" if gpu_available else "CPU"
        
        print(f"GPU Available: {gpu_available}")
        print(f"Backend: {backend}")
        print(f"Device Info: {device_info}")
        
        if gpu_available:
            print("✅ GPU system initialized successfully")
        else:
            print("⚠️ GPU not available, using CPU fallback")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU initialization error: {e}")
        return False

def test_session_state():
    """Test session state management."""
    print("\nTesting session state management...")
    
    try:
        # Mock streamlit session state for testing
        class MockSessionState:
            def __init__(self):
                self._state = {}
            
            def get(self, key, default=None):
                return self._state.get(key, default)
            
            def __setitem__(self, key, value):
                self._state[key] = value
            
            def __getitem__(self, key):
                return self._state[key]
            
            def __contains__(self, key):
                return key in self._state
        
        # Mock streamlit for testing
        import streamlit as st
        if not hasattr(st, 'session_state'):
            st.session_state = MockSessionState()
        
        from common.session_state import initialize_session_state
        
        initialize_session_state()
        
        # Check if required session state variables are initialized
        required_vars = [
            'gpu_available', 'gpu_backend', 'processing_mode',
            'selected_analysis_mode', 'gpu_batch_size'
        ]
        
        for var in required_vars:
            if var in st.session_state:
                print(f"✅ {var} initialized")
            else:
                print(f"❌ {var} not initialized")
                return False
        
        print("✅ Session state management working")
        return True
        
    except Exception as e:
        print(f"❌ Session state error: {e}")
        return False

def test_processing_config():
    """Test processing configuration functions."""
    print("\nTesting processing configuration...")
    
    try:
        from utils.gpu_utils import get_optimal_processing_config
        from common.constants import ANALYSIS_MODES
        
        for mode in ANALYSIS_MODES:
            config = get_optimal_processing_config(mode, 1000)  # Test with 1000 traces
            
            required_keys = ['backend', 'batch_size', 'estimated_batches', 'memory_usage']
            for key in required_keys:
                if key not in config:
                    print(f"❌ Missing key '{key}' in config for mode '{mode}'")
                    return False
            
            print(f"✅ Config for '{mode}': {config['backend']} backend, batch size {config['batch_size']}")
        
        print("✅ Processing configuration working")
        return True
        
    except Exception as e:
        print(f"❌ Processing configuration error: {e}")
        return False

def test_export_functions():
    """Test export utility functions."""
    print("\nTesting export functions...")
    
    try:
        from utils.export_utils import validate_export_data, create_csv_export
        
        # Create mock results data
        mock_results = {
            'descriptors': [
                {'WOSS': 1.5, 'hfc': 0.8, 'spec_slope': -0.2},
                {'WOSS': 2.1, 'hfc': 1.2, 'spec_slope': -0.1}
            ],
            'trace_data': [[1, 2, 3], [4, 5, 6]]
        }
        
        selected_attributes = ['WOSS', 'hfc']
        
        # Test validation
        is_valid = validate_export_data(mock_results, selected_attributes)
        if is_valid:
            print("✅ Export data validation working")
        else:
            print("❌ Export data validation failed")
            return False
        
        # Test CSV export (without actually creating file)
        print("✅ Export functions available")
        return True
        
    except Exception as e:
        print(f"❌ Export functions error: {e}")
        return False

def test_visualization():
    """Test visualization functions."""
    print("\nTesting visualization functions...")
    
    try:
        from utils.visualization import create_analysis_plots
        
        # Create mock results data
        mock_results = {
            'descriptors': [
                {'WOSS': 1.5, 'hfc': 0.8},
                {'WOSS': 2.1, 'hfc': 1.2}
            ]
        }
        
        mock_plot_settings = {'freq_min': 0, 'freq_max': 125}
        
        fig = create_analysis_plots(mock_results, "Single inline (all crosslines)", mock_plot_settings)
        
        if fig is not None:
            print("✅ Visualization functions working")
            return True
        else:
            print("❌ Visualization returned None")
            return False
        
    except Exception as e:
        print(f"❌ Visualization error: {e}")
        return False

def main():
    """Run all integration tests."""
    print("=" * 60)
    print("WOSS Seismic Analysis Tool - Integration Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("GPU Initialization", test_gpu_initialization),
        ("Session State", test_session_state),
        ("Processing Config", test_processing_config),
        ("Export Functions", test_export_functions),
        ("Visualization", test_visualization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Integration Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All integration tests PASSED! The application is ready for use.")
        return True
    else:
        print("⚠️ Some integration tests FAILED. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
