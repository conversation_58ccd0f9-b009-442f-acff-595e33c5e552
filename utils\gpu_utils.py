# utils/gpu_utils.py
import logging
import streamlit as st
import numpy as np

# Global GPU state
GPU_AVAILABLE = False
GPU_DEVICE_INFO = None
CPU_FALLBACK_ENABLED = True

@st.cache_resource
def initialize_gpu_system():
    """Initialize and verify GPU processing capability."""
    global GPU_AVAILABLE, GPU_DEVICE_INFO

    try:
        import cupy as cp

        # Test GPU functionality with realistic workload
        test_size = 1000
        test_data = cp.random.random((test_size,), dtype=cp.float32)
        test_fft = cp.fft.fft(test_data)
        test_result = cp.abs(test_fft)

        # Get device information
        device = cp.cuda.Device()
        GPU_DEVICE_INFO = {
            'name': device.attributes['name'],
            'compute_capability': device.compute_capability,
            'memory_total': device.mem_info[1],
            'memory_free': device.mem_info[0]
        }

        GPU_AVAILABLE = True
        logging.info(f"GPU processing enabled: {GPU_DEVICE_INFO['name']}")
        return True, "CuPy", GPU_DEVICE_INFO

    except ImportError:
        logging.warning("CuPy not available - CPU fallback enabled")
        return False, "CPU Fallback", None
    except Exception as e:
        logging.error(f"GPU initialization failed: {e}")
        return False, "CPU Fallback", str(e)

def get_processing_backend():
    """Get the active processing backend (GPU or CPU)."""
    if GPU_AVAILABLE:
        try:
            import cupy as cp
            from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu
            return "GPU", dlogst_spec_descriptor_gpu
        except ImportError:
            pass
    
    # Fallback to CPU
    from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
    return "CPU", dlogst_spec_descriptor_cpu

def get_optimal_processing_config(analysis_mode, num_traces):
    """Get optimal processing configuration for the analysis mode."""
    from common.constants import GPU_BATCH_SIZES, CPU_FALLBACK_BATCH_SIZES

    if GPU_AVAILABLE:
        batch_size = GPU_BATCH_SIZES.get(analysis_mode, 512)
        processing_backend = "GPU"

        # Adjust batch size based on available memory
        if GPU_DEVICE_INFO:
            memory_gb = GPU_DEVICE_INFO['memory_total'] / (1024**3)
            if memory_gb < 4:  # Low memory GPU
                batch_size = min(batch_size // 2, 256)
            elif memory_gb > 16:  # High memory GPU
                batch_size = min(batch_size * 2, 2048)
    else:
        batch_size = CPU_FALLBACK_BATCH_SIZES.get(analysis_mode, 32)
        processing_backend = "CPU"

    return {
        'backend': processing_backend,
        'batch_size': batch_size,
        'estimated_batches': (num_traces + batch_size - 1) // batch_size,
        'memory_usage': 'optimized'
    }

def clear_gpu_memory():
    """Clear GPU memory pools and force garbage collection."""
    if GPU_AVAILABLE:
        try:
            import cupy as cp
            mempool = cp.get_default_memory_pool()
            pinned_mempool = cp.get_default_pinned_memory_pool()
            mempool.free_all_blocks()
            pinned_mempool.free_all_blocks()
            logging.info("GPU memory cleared")
        except Exception as e:
            logging.warning(f"Failed to clear GPU memory: {e}")

def get_processing_functions():
    """Get the appropriate processing functions based on GPU availability."""
    if GPU_AVAILABLE:
        try:
            from .dlogst_spec_descriptor_gpu import (
                dlogst_spec_descriptor_gpu,
                dlogst_spec_descriptor_gpu_2d_chunked
            )
            return {
                'single': dlogst_spec_descriptor_gpu,
                'batch': dlogst_spec_descriptor_gpu_2d_chunked,
                'backend': 'GPU'
            }
        except ImportError:
            pass

    # Fallback to CPU
    from .dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
    return {
        'single': dlogst_spec_descriptor_cpu,
        'batch': None,  # CPU processes single traces
        'backend': 'CPU'
    }

def optimize_batch_size_for_mode(mode, base_size=512):
    """Optimize batch size based on analysis mode and GPU memory."""
    if not GPU_AVAILABLE:
        return min(base_size // 4, 128)  # Conservative CPU batch size

    try:
        import cupy as cp
        mempool = cp.get_default_memory_pool()
        free_bytes = mempool.free_bytes()
        total_bytes = mempool.total_bytes()

        # Mode-specific optimization
        if mode in ["Single inline (all crosslines)", "Single crossline (all inlines)"]:
            return min(base_size * 2, 1024)  # Larger batches for line processing
        elif mode == "By inline/crossline section (AOI)":
            return base_size  # Standard batch size for AOI
        elif mode == "By Polyline File Import":
            return min(base_size // 2, 256)  # Smaller batches for complex polyline processing
        else:
            return base_size

    except Exception:
        return base_size
